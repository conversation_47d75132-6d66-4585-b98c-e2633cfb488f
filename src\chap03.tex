% !TeX root = ../main.tex

\chapter{前导码设计与同步方案}

由于频率、符号速率和调制方式均已确定，在物理层我们主要需要设计前导码（Preamble），并利用其完成信号检测、载波同步、符号同步和消息头检测这四大功能，为正常接收正文符号做准备。
在这一部分，我们考察的是一个基带信号数字接收机，其输入为复值采样序列，输出则希望是无误码的消息。

\section{信号检测}

首先，面对一长串消息开始时间未知的信号，接收机需要检测出消息的到来时刻。
为此，我们在前导码的开头设置了2个1比特用于信号与噪声的分离。
下面使用了三种方法来实现信号能量的检测。

\subsection{阈值法}

阈值法是最自然能想到的方法。其工作流程如下：
\begin{itemize}
  \item 在上一个消息结束后进入预热状态，采集连续若干个点（这里使用40个点）作为噪声样本；
  \item 取这些噪声样本的能量（幅度值的平方）的四倍作为阈值，判断信号中是否有能量超过该阈值的样点；
  \item 如果有连续两个符号长度的样点的能量超过阈值，则认为信号到来，否则计数器清零。
\end{itemize}

在实机测试中，阈值法虽然能够应对不同的信号强度，但前提需要噪声功率的长时间稳定。
如果预热期间的噪声功率较小、而之后变大，阈值法则可能将后面的噪声误判为信号。
反之，如果预热期间的噪声功率较大，而信号功率相对较小（小于噪声功率的四倍），那么阈值法就将漏判信号的到来。

究其原因，这是因为阈值法没有充分利用信号和噪声的自相关特性（信号在时域上强相关、而噪声在时域上相关性较小），所以即便可以通过动态阈值调整策略改善其错判、漏判情况，阈值法仍不是信号检测的最优方法。

\subsection{双滑动窗法}

双滑动窗法是适用于突发信号的一种盲检测算法，其则利用了信号和噪声的自相关特性，不需要噪声功率保持稳定，也对信噪比要求不高。

其工作流程如下图所示。时域上有两个相邻的长度相等的滑动窗$W_1$和$W_2$，我们先对两个滑动窗内的样点能量进行求和，然后计算这两个能量和的比值。
\begin{equation}
  A_1 = \sum_{W_1} x(n)^2, \quad A_2 = \sum_{W_2} x(n)^2
\end{equation}
\begin{equation}
  r_{12} = \frac{A_1}{A_2}, \quad r_{21} = \frac{A_2}{A_1}
\end{equation}

若$W_1$和$W_2$窗内均为噪声，则$A_1$和$A_2$均代表了噪声功率，在前后数十个采样点内可以认为是短时稳定的。因此比值$r_{12}$和$r_{21}$的值应该均在1附近。

若$W_1$和$W_2$窗内均为信号，而信号功率恒定，那么比值$r_{12}$和$r_{21}$同样应在1附近。

若$W_1$窗内为噪声、$W_2$窗内为信号（即发射信号开始处），那么由于信号功率远大于平均噪声功率，此时$r_{21}$的值会远大于1，而$r_{12}$的值则会远小于1。

若$W_1$窗内为信号、$W_2$窗内为噪声（即发射信号结束处），同理可得$r_{12}$的值会远大于1，而$r_{21}$的值则会远小于1。

经过上述分析，我们发现比值$r_{21}$在发射信号开始处会出现一个远大于1的峰值，而$r_{12}$则会在发射信号结束处出现一个远大于1的峰值。我们也就可以据此来判断信号开始于结束的时刻。

下面，我们使用一段模拟信号来检验其检测性能。信号由20个0、20个1、20个0的方波比特序列在基带生成，信号通过一个具有频偏、相偏的高斯白噪声信道模型来到接收机处，接收机再对收到的信号进行双滑动窗法能量检测。
频偏设置为10kHz、相偏设置为0.3（理论上不应影响检测结果）、信号与噪声标准差的幅度之比设置为10:1（信噪比为20dB）。仿真结果如下图所示

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/signal_detect.png}
  \caption{双滑动窗法检测结果}
  %\label{fig:3-1}
\end{figure}

可以观察到$r_{21}$和$r_{12}$两个比值的确在信号发射开始处和结尾处出现了两个远大于1的峰，而在其余时间内均保持在10以内。
因此，我们将阈值设定为25，即可以判定信号的开始与结束。
此时，为了保证不漏检，信号与噪声的能量比值需要大于25:1，对应信噪比为14dB，已经低于第2章计算得到的最低信噪比，因此可以认为这个阈值不会漏检协议通信能力范围内的信号。
同时，实机验证也发现，在实际物理环境的噪声中，$r_{21}$和$r_{12}$波动略大于仿真结果，但不会超过25的阈值。因此阈值的设置也很好地避免了将噪声误判为信号。

\subsection{过零点数量筛选}

在图中，我们也可以观察到，$r_{21}$和$r_{12}$的峰值区域宽度较大，且可能出现多个峰值。
这主要是因为经过带通滤波的信号在实际符号开始前和结束后，都在噪声基础上叠加了一部分滤波器响应，使得信号能量并非瞬间上升或下降。
这在检测信号结束时刻时没有影响，但在检测信号开始时刻时，就会导致判断时机早于实际信号开始时间约2个符号时间（如图中红色虚线所示）。

同时，当噪声能量较大时，由于两侧的滤波器响应的能量与噪声能量相当，因此当滑动窗$W_2$移动到实际符号开始前的滤波器响应处时，比值$r_{21}$就不会超过阈值，导致判断得到的信号开始时间与实际开始时间相符。
因此，噪声能量的差异会导致双滑动窗法检测到的信号开始时间前后发生至少2个符号的偏差，这并不是我们所希望的。

为了解决这个问题，我们可以利用信号的过零率来辅助进行判断。
从图中可以观察到，无论是噪声还是滤波器响应段，其复信号过零点数量均较多；
而信号起始段为单频信号，其最小周期（10 kHz，100 us，详见载波同步部分的分析）远大于滑动窗长，因此在滑动窗内的过零点数量不超过1个。

我们可以利用这一特征筛选滑动窗$W_2$内的信号，若过零点数量超过1个，则将比值$r_{21}$置为0，从而避免将其误判为信号的开始时刻。
下图时应用了这一个改进后的检测结果。可以看到$r_{21}$的峰值区域宽度已经大大减小，且利用之前给出的阈值，我们已经可以准确地检测到信号的开始时刻。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/signal_detect_improved.png}
  \caption{加入过零率筛选后的双滑动窗法检测结果}
  %\label{fig:3-2}
\end{figure}

\section{载波同步}

由于发射机和接收机各自使用的频率源（通常为晶振）的频率偏差、以及发射机与接收机之间相对运动导致的多普勒频移，接收机接收到的基带信号会在原始信号的基础上叠加一个频率分量，使得我们无法对信号直接进行判决。
同时，接收机的载波信号与发射机的载波信号也会天然存在一个相位偏差，使接收到信号的星座图发生偏转、产生相位模糊，从而使解码得到的符号出错。

载波同步就是准确估计出发射机的载波频率和相位，并将接收机的频率、相位与其同步的过程。载波同步的方法分为插入导频法和直接提取法两种。
其中插入导频法适用于OFDM系统，是将某个子载波作为导频传输单音信号，接收机根据在这个子载波上接收到的信号进行频率和相位估计。
而直接提取法则利用信号本身的特点，去除所传输的符号的影响，提取出频偏和相偏来进行同步。\cite{nasirTimingCarrierSynchronization2016}

由于本协议不采用OFDM，因此我们采用直接提取法进行载波同步。

\subsection{最大频偏和同步要求}

为了合理地设计载波同步方案，我们首先需要估计出最大可能的载波频偏。
载波频偏主要有两个来源：发射机和接收机各自使用的频率源（通常为晶振）的频率偏差、发射机与接收机之间相对运动导致的多普勒频移，我们分别估算这两个来源可能导致的最大频偏，然后相加得到总的最大频偏。

对于频率源的频率偏差，查阅晶振数据手册可知，常见的晶振频率精度为$\pm 30\ \text{ppm}$以内（包括调整频偏和温度频偏）。
若发射机和接收机电路不引入额外的频率偏置，按射频中心频率1443MHz计算，最大频偏为
\begin{equation}
    \Delta f_1 = f_c \times 2\text{ppm} = 1443\times10^6\times2\times30\times10^{-6} = 86.58\text{kHz}
\end{equation}
其中$f_c$为射频中心频率，ppm为频率精度。

对于多普勒频移，查阅相关资料可知，低空飞行器中飞行速度最高的是直升机和小型固定翼飞机，其最高速度（地速）一般不超过360km/h，即100m/s。
我们按两架直升机按最高速度相向飞行来计算，相对运动导致的最大多普勒频移为
\begin{equation}
    \Delta f_2 = (\frac{c+v_{max}}{c-v_{max}}-1)f_c = (\frac{3\times10^8+100}{3\times10^8-100}-1)\times1443\times10^6 = 962\text{Hz}
\end{equation}
其中$c$为光速，$v_{max}$为最大相对运动速度。
可以看到，在这一速度下，多普勒频移要远小于频率源的频率偏差，对于载波同步的影响可以几乎忽略不计。

因此，总的最大频偏为
\begin{equation}
    \Delta f = \Delta f_1 + \Delta f_2 = 86.58\text{kHz} + 962\text{Hz} = 87.54\text{kHz}
\end{equation}
也就是说，接收机要能够承受在基带最多$\pm 87.54\text{kHz}$的额外频率分量，并正确估计和消除它。

除了最大频偏，我们还需要先计算出在接收消息正文时，不影响解码的最大频偏，这也是同步完成的标志。
由于我们使用16QAM调制方式（矩形星座图），其符号之间的最小相位差对应$3+1j$和$1+1j$两点之间的夹角（如下图所示），即
\begin{equation}
    \Delta \theta_{min} = \arctan{\frac{1}{1}} - \arctan{\frac{1}{3}} = 0.4636\text{rad}
\end{equation}

我们认为当接收到的符号与对应星座图位置的夹角小于上述最小相位差的十分之一时，残余的频偏引入的频率分量不影响解码，误码的主要来源为噪声。
那么，在无噪声前提下，如果接收机处于载波同步状态，前一个符号的相位角与对应星座图位置的相位角一致，则后一个符号的相位角与对应星座图位置的夹角不能大于上述最小相位差的十分之一。
此时，该相位差完全由残余的频偏引入的频率分量造成，对应的频率为
\begin{equation}
    \Delta f_{sync} = \frac{0.1 \times\Delta \theta_{min}}{2\pi T_{sym}} = \frac{0.1\times 0.4636}{2\pi\times667\times10^{-9}} = 11.06\text{kHz}
\end{equation}
其中$T_{sym}$为单个符号的周期。

也就是说，当接收到的信号的载波频率与接收机内部载波频率之差小于11.06kHz时，即可认为载波同步完成。

\subsection{Costas环原理简介}

接下来，我们使用Costas环来实现载波同步。
Costas环由美国工程师J.P.Costas于1956年提出，是一种广泛使用的载波同步方法。
我们这里使用的是适用于数字信号处理的改进型数字域Costas环，其原理框图如下图所示。\cite{bestCostasLoops2018}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/costas_diagram.png}
  \caption{Costas环原理框图}
\end{figure}

改进型数字域Costas环由相位检测器（Phase Detector, PD）、环路滤波器（Loop filter, LF）、和压控振荡器（Voltage Controlled Oscillator, VCO）组成。
其中相位检测器与所使用的调制方式有关，环路滤波器和压控振荡器则对所有调制方式均通用。

当输入采样复信号$x_0(n)=m(n)e^{j\phi(n)}$（其中$m(n)$为消息符号，$\phi(n)$为载波相位）时，
假设此时压控振荡器的输出为$x_1(n)=e^{-j\phi'(n)}$，则乘法器输出为
\begin{equation}
    x_2(n) = x_0(n) \cdot x_1(n) = m(n)e^{j(\phi(n) - \phi'(n))}
\end{equation}
这也是相位检测器的输入。

相位检测器需要排除消息符号$m(n)$的影响，从而提取上面信号中的相位估计误差$\phi(n) - \phi'(n)$。
因此，相位检测器先对输入信号进行判决，然后输出判决得到的符号与输入信号在星座图上的夹角。
其数学形式如下所示。
\begin{equation}
    \theta(n) = \angle x_2(n) - \angle\hat{m}(n) = \phi(n) - \phi'(n)
\end{equation}
其中$\hat{m}(n)$为判决得到的符号。

值得一提的是，对于幅度调制的信号（直流信号），当符号幅度不为0时，相位检测器的输出即为输入信号的相位$\theta(n) = \angle x_2(n)$。

相位检测器的输出接到环路滤波器的输入。环路滤波器的主要作用是滤除噪声，使输出的频率收敛到正确的载波频偏。
它实际上就是一个低通滤波器。我们这里使用它的二阶数字形式，传递函数为
\begin{equation}
    H(z) = C_1+C_2\frac{1}{1-z^{-1}}
\end{equation}
其中$C_1$和$C_2$为滤波器的系数，由环路自然角频率$\omega_n$和阻尼系数$\xi$确定，以下是它们的数学关系
\begin{equation}
    C_1 = \frac{8\xi \omega_n T}{4+4\xi\omega_n T + (\omega_n T)^2}
\end{equation}
\begin{equation}
    C_2 = \frac{4(\omega_n T)^2}{4+4\xi\omega_n T + (\omega_n T)^2}
\end{equation}
其中$T$为采样周期。

在实际应用中，阻尼系数$\xi$通常取0.707，而环路自然角频率可以用噪声带宽$B_n$来表示
\begin{equation}
    \omega_n = \frac{8\xi B_n}{4\xi^2+1}
\end{equation}
其中噪声带宽$B_n$决定了环路滤除噪声的效果，$B_n$越小，滤波效果越好，但收敛到正确载波频率所需的时间越长、可同步成功的最大频偏也越小。
一般工程中$B_n\cdot T$不超过0.1。

在确定好阻尼系数$\xi$和噪声带宽$B_n$后，我们就可以计算出滤波器的系数$C_1$和$C_2$，环路滤波器也就随之确定。
下图还给出了数字环路滤波器的系统框图。\cite{Yang2011}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/loop_filter.png}
  \caption{数字环路滤波器原理框图}
\end{figure}

环路滤波器的输出接到压控振荡器的输入。压控振荡器包含一个积分器，用于更新$\phi'(n)$，其数字形式传递函数为
\begin{equation}
    H(z) = \frac{1}{1-z^{-1}}
\end{equation}
其中$T$为采样周期。

记环路滤波器输出为$LF[\phi(n) - \phi'(n)]$，则压控振荡器的$\phi'(n)$的更新方程为
\begin{equation}
    \phi'(n+1) = \phi'(n) + LF[\phi(n) - \phi'(n)]
\end{equation}

压控振荡器生成本地频率$x_1(n+1)=e^{-j\phi'(n+1)}$后与下一个采样相乘，从而形成了闭环。
消除载波频偏后的信号$x_2(n)=m(n)e^{j(\phi(n) - \phi'(n))}$可同步进行后续信号处理。

当载波同步完成时，$\phi'(n)$应与$\phi(n)$一致，这意味着环路滤波器的输出$LF[\phi(n) - \phi'(n)]$就是载波频偏在一个周期内导致的相位差$\Delta f T$。
若载波频偏实际高于环路滤波器输出所对应的值，那么$\phi(n) - \phi'(n)$为正，环路滤波器输出为正，其输出会增加，并通过滤波最终收敛到正确的载波频偏。反之亦然。

\subsection{同步策略}

根据Costas环线性模型的理论分析\cite{bestCostasLoops2018}，Costas环的线性锁定范围为
\begin{equation}
  \Delta \omega_L = \pi\xi\omega_n
\end{equation}
其中$\Delta \omega_L$为载波角频偏，小于该值的角频偏即位于线性锁定范围内。

在这个线性锁定范围内，Costas环的最大锁定时间为
\begin{equation}
  T_L = \frac{2\pi}{\omega_n}
\end{equation}

这两者均由环路自然角频率$\omega_n$和阻尼系数$\xi$（等价于噪声带宽$B_n$和阻尼系数$\xi$）确定。
而为了保证同步过程平稳、振荡较少，阻尼系数$\xi$一般固定为0.707。因此，我们主要需要选定合适的噪声带宽$B_n$。

前面提到，除了线性锁定范围和锁定时间外，环路噪声带宽$B_n$还决定了环路滤除噪声的效果。
这不仅与同步过程有关：在同步完成后，Costas环输出的频偏估计也会因为输入信号中的噪声而产生波动。
噪声带宽$B_n$越大，Costas环输出的频偏估计的波动就越大，导致符号的相位偏差也越大，这也是我们不希望看到的。

以下给出了在不同信噪比条件下，频偏估计的误差最大值与归一化的噪声带宽$B_n\cdot T$的关系。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/lock_noise.png}
  \caption{Costas环的频偏估计误差最大值与归一化噪声带宽的关系}
  % %\label{fig:3-2}
\end{figure}
为了可读性，图中纵轴（表示频率）使用了对数坐标，但在信噪比相同时，频偏估计的误差最大值与噪声带宽呈现线性关系。

根据噪声带宽与线性锁定范围、锁定时间、和同步后频偏估计的误差最大值的关系，我们可以得出噪声带宽的一个合适的参数区间。
由最大频偏$\Delta f = 87.54\text{kHz}$，可以得到线性锁定范围至少为
\begin{equation}
  \Delta \omega_{L,min} = 2\pi\times \Delta f = 2\pi\times 87.54\times10^3 = 5.5\times 10^5\text{rad/s}
\end{equation}

代入线性锁定范围的公式，可以得到噪声带宽的下限为
\begin{equation}
  B_{n,min} = \frac{4\xi^2+1}{8\xi} \omega_{n,min} = \frac{4\xi^2+1}{8\xi}\cdot\frac{1}{\pi\xi} \Delta \omega_{L,min} = \frac{4\times0.707^2+1}{8\times0.707}\cdot\frac{1}{\pi\times0.707}\cdot 5.5\times 10^5 = 1.31\times 10^5 \text{rad/s}
\end{equation}

对应归一化的噪声带宽为
\begin{equation}
  B_{n,min}\cdot T = 1.31\times 10^5 \times 667\times10^{-9} = 0.088
\end{equation}

而根据同步要求，频偏估计的误差最大值不能超过$\Delta f_{sync} = 11.06\text{kHz}$，其归一化值为
\begin{equation}
  \frac{\Delta f_{sync}}{R_s} = \frac{11.06\times10^3}{1.5\times10^6} = 7.37\times10^{-3}
\end{equation}
按照频偏估计误差最大值与噪声带宽的关系，若要使信噪比为15dB的信号满足同步要求，则归一化的噪声带宽不能高于约0.05。

我们发现锁定范围与频偏波动对噪声带宽的要求是矛盾的，因此需要在两者之间找到一个折衷的方案。
于是，我们设计了粗同步与精同步相结合的方案，先通过粗同步将频偏削减到较小范围，再通过精同步缩减到目标范围。

对于粗同步，我们使用幅度调制的信号（单音序列），将过采样后的采样点作为Costas环的输入（这里使用的采样率为6Msps，是符号速率的4倍，对应过采样率为3）。
那么，归一化的噪声带宽下限减小为
\begin{equation}
  B_{n,min}\cdot T = \frac{B_{n,min}}{R_{samp}} = \frac{1.31\times 10^5}{6\times10^6} = 0.022
\end{equation}

我们保留一些裕量，选取噪声带宽为0.03，则按照频偏估计误差最大值与噪声带宽的关系，信噪比为15dB的信号在同步后的频偏估计误差最大值约为$4\times 10^{-3}$，对应最大残余频偏为
\begin{equation}
  \Delta f_{res} = 4\times 10^{-3} \times 6\times10^6 = 24\text{kHz}
\end{equation}

同时，理论最大锁定时间（以采样点为单位）为
\begin{equation}
  \frac{T_L}{T_{samp}} = \frac{2\pi}{\omega_n T_{samp}} = 2\pi\frac{4\xi^2+1}{8\xi B_n T_{samp}} = 2\pi\frac{4\times0.707^2+1}{8\times0.707\times0.03} = 111\text{个采样点}
\end{equation}

而对于精同步，我们使用BPSK调制的信号，以符号作为Costas环的输入。
在这一阶段，我们只需将频偏由24kHz缩减到同步目标11kHz。
为了得到噪声带宽的合理参数区间，我们需要重复之前的计算过程。
首先计算线性锁定范围，它需要至少为
\begin{equation}
  \Delta \omega_{L,min} = 2\pi\times \Delta f_{res} = 2\pi\times 24\times10^3 = 1.5\times 10^5\text{rad/s}
\end{equation}

代入线性锁定范围的公式，可以得到噪声带宽的下限为
\begin{equation}
  B_{n,min} = \frac{4\xi^2+1}{8\xi} \omega_{n,min} = \frac{4\xi^2+1}{8\xi}\cdot\frac{1}{\pi\xi} \Delta \omega_{L,min} = \frac{4\times0.707^2+1}{8\times0.707}\cdot\frac{1}{\pi
  \times0.707}\cdot 1.5\times 10^5 = 1.8\times 10^4 \text{rad/s}
\end{equation}

对应归一化的噪声带宽为
\begin{equation}
  B_{n,min}\cdot T = \frac{B_{n,min}}{R_{sym}} = \frac{0.073\times 10^5}{1.5\times10^6} = 0.024
\end{equation}

而根据同步要求读出的归一化噪声带宽的上限为0.05，因此我们同样可以取归一化噪声带宽为0.03。

由此，我们也可以反推出同步后的频偏估计误差最大值为$4\times 10^{-3}$，对应最大残余频偏为
\begin{equation}
  \Delta f_{res} = 4\times 10^{-3} \times 1.5\times10^6 = 6\text{kHz}
\end{equation}

同时，理论最大锁定时间（以符号为单位）为
\begin{equation}
  \frac{T_L}{T_{sym}} = \frac{2\pi}{\omega_n T_{sym}} = 2\pi\frac{4\xi^2+1}{8\xi B_n T_{sym}} = 2\pi\frac{4\times0.707^2+1}{8\times0.707\times0.03} = 111\text{个符号}
\end{equation}

\subsection{仿真结果}

如果按照上一小节的理论最大锁定时间来计算，那么载波同步所需要的时间为
\begin{equation}
  T_{sync} = 111\times T_{samp} + 111\times T_{sym} = 111\times 667\times10^{-9}\times(1+\frac{1}{4}) = 92\text{us}
\end{equation}
已经超过了消息正文的长度。

然而，这是理论最大锁定时间，实际让频偏估计落入阈值范围内并不需要这么久的时间。
那么，载波同步究竟需要多长时间？以及频偏估计是否能落入阈值范围内？
下面，我们利用仿真模拟来验证。

首先是粗同步阶段，下图给出了不同信噪比条件下，单频信号的锁定时间（以采样点为单位）与频偏的关系。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/lock_time_monotone.png}
  \caption{单频信号的锁定时间}
  % %\label{fig:3-3}
\end{figure}
可以看到，锁定时间的上限约为40个采样点，即$40/4=10$个符号，因此我们在信号检测的2个1比特之后，再发射10个1比特，称为S0序列。

然后是精同步阶段，下图给出了不同信噪比条件下，BPSK调制信号的锁定时间（以符号为单位）与频偏的关系。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/lock_time_bpsk.png}
  \caption{BPSK调制信号的锁定时间}
  % %\label{fig:3-4}
\end{figure}
可以看到，锁定时间的上限约为8个符号，因此我们在前面10个1比特之后增加8个BPSK符号序列。

\section{符号同步}

由于发射机和接收机的时钟相位是随机的，因此接收机采样接收到的信号形成的采样点与信号的符号点有一定的时间距离，称为定时偏差。
此时，如果直接使用采样点进行判决，不仅会产生幅度失真，还引入了符号间串扰，可能导致判断结果出错。
同时，对于采样率大于符号速率的情况，我们也无法选取合适的采样点作为符号点进行后续的信号处理。

这时候就需要进行符号同步，其作用是将接收机的采样时钟与接收到信号的符号时钟同步，从而使采样点与符号点对齐。
在实现方法上，符号同步主要分为反馈式和前馈式算法。
反馈式算法包括MM算法、早迟门算法、Gardner算法等，其基本思想是通过相邻采样点的值来估计定时偏差，并通过锁相环路反馈给采样时钟，采样时钟调整相位后进行下一轮估计，直至环路锁定。
而前馈式算法则通过分析采样点的统计特性，直接估计定时偏差，然后通过插值等方法对利用采样点还原出符号点。

反馈式算法的优点是计算成本较低，采样率只要求为符号速率的2倍甚至1倍，且可以随时追踪定时偏差的变化。
但缺点是依赖于锁相环路来消除定时偏差，同步时间较长，且需要硬件支持。
而前馈式算法则是开环控制的，估计定时偏差的时间较短、也无需硬件支持。
但是使用前馈式算法要求采样率大于符号速率的2倍（通常为4倍），计算成本相对较大，同时估计出的定时偏差是一次性的，无法追踪定时偏差的变化。

从上述特点可以看出，反馈式算法更适合时间尺度较长的通信场景，其对同步时间要求不高，但通信过程中定时误差可能会产生较大变化。
而前馈式算法更适合时间尺度较短的短包通信，同步时间要尽可能地短，但通信过程中定时误差不会有很大的变化。
由于我们所设计的协议属于短包通信，因此我们选择使用前馈式算法来实现符号同步。

\subsection{同步要求}

在实现算法或仿真验证前，我们需要先明确符号同步的标志，或者说定时偏差估计误差的最大允许值。

下图展示了一段经过升余弦滤波的16QAM信号实部，其符号序列为$(-3,3,-3,3,3,-3,-3,3,-3,3)$（图中红点）。
由于升余弦滤波器冲激响应的特性，这个符号序列可以使第5和第6个符号之间的信号尽可能快的接近零点。
这样一来，如果定时偏差的估计误差较大，则还原出的第5个符号就很可能落入2以下的区域（图中水平灰色虚线之间的区域），从而使解码得到的符号出错。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/sample_offset.png}
  \caption{符号同步的信号示意图}
  % %\label{fig:3-4}
\end{figure}

利用这一最坏情形，我们可以读出定时偏差估计误差的最大允许值为0.1625个符号周期，这也就是符号同步的最低要求。

此外，我们还可以估算发射机和接收机的频率源频率偏差对定时偏差的影响，来检验一次性的符号同步是否可靠。
与载波同步类似，我们取晶振的频率偏差为$\pm 30\ \text{ppm}$，那么符号速率的范围为
\begin{equation}
  R_s = R_{s0} \times (1 \pm \text{ppm}) = 1.5\times10^6\times(1 \pm 30\times10^{-6}) = (1.5\times10^6 \pm 45)\text{Hz}
\end{equation}

对应符号周期的范围为
\begin{equation}
  T_s = \frac{1}{R_s} = \frac{1}{1.5\times10^6 \pm 45} = (666.67 \pm 0.02)\text{ns}
\end{equation}

那么，经过100个符号周期（即协议的消息长度）的累积，额外引入的定时偏差最大为
\begin{equation}
  \Delta T_{clk} = 100 \times 2 \times 0.02\text{ns} = 4\text{ns}
\end{equation}
对应为$4/667 = 0.006$个符号周期。

这与我们前面得到的定时偏差估计误差的最大允许值相比，小了两个数量级，因此可以认为频率源的频率偏差在单个消息中引入的定时偏差不会影响符号判决，可以忽略不计。
这也说明了使用前馈式算法进行一次性的定时偏差估计是可靠的。

\subsection{前馈式定时误差估计算法原理简介}

下面简要介绍前馈式定时误差估计算法的原理。\cite{Oerder1988}
为了说明的方便和数学推导的简洁，我们假设符号同步在无噪声的环境中进行。
由于噪声的期望为0在时间上不相关，因此在实际有噪声的环境中，噪声也不影响定时偏差的估计结果。

在经过匹配滤波和采样后，采样点的幅度值可表示为
\begin{equation}
  r(\frac{kT}{N}) = |\sum_{n=-\infty}^{\infty} a_n\cdot h(\frac{kT}{N}-nT-\epsilon T)|
\end{equation}
其中$k$为采样点的索引，$T$为符号周期，$N$为采样速率与符号速率的比值（需是整数），$a_n$为第$n$个符号的幅度值，$h(t)$为匹配滤波器平方后的冲激响应，$\epsilon$为定时偏差（以符号周期为单位，范围是$[-0.5, 0.5)$）。

将采样点$r(\frac{kT}{N})$平方后做离散傅里叶变换，取频率为$\frac{1}{T}$的点，可得
\begin{equation}
  R(\frac{1}{T}) = \sum_{k=0}^{N-1} r^2(\frac{kT}{N})e^{-j\frac{2\pi kT}{N}\cdot\frac{1}{T}} = \sum_{k=0}^{N-1} r^2(\frac{kT}{N})e^{-j\frac{2\pi k}{N}}
\end{equation}

至此，定时误差的估计值可以表示为
\begin{equation}
  \hat{\epsilon} = -\frac{1}{2\pi}\angle R(\frac{1}{T})
\end{equation}

下面我们通过计算证明$\mathbf{E}[\hat{\epsilon}] = \epsilon$，即上述统计量的确是定时偏差的无偏估计。

先计算$|r(\frac{kT}{N})|^2$的期望值。
\begin{align*}
  \mathbf{E}[r^2(\frac{kT}{N})] & = \mathbf{E}[|\sum_{n=-\infty}^{\infty} a_n\cdot h(\frac{kT}{N}-nT-\epsilon T)|^2] & \\
  & = \sum_{n=-\infty}^{\infty} \sum_{m=-\infty}^{\infty} \mathbf{E}[a_n\cdot a_m^*]\cdot h(\frac{kT}{N}-nT-\epsilon T)\cdot h^*(\frac{kT}{N}-mT-\epsilon T) & \\
  & = \sum_{n=-\infty}^{\infty} |h(\frac{kT}{N}-nT-\epsilon T)|^2
\end{align*}

然后，利用离散傅里叶变换与连续时间傅里叶变换的关系，我们可以得到
\begin{equation}
  \mathbf{E}[R(\frac{1}{T})]
  = \frac{N}{T}\mathfrak{F}[|h(t-\epsilon T)|^2]_{f=\frac{1}{T}}
  = \frac{N}{T}\mathfrak{F}[|h(t)|^2]_{f=\frac{1}{T}}\cdot e^{-j2\pi\frac{\epsilon T}{T}}
  = \frac{N}{T}\mathfrak{F}[|h(t)|^2]_{f=\frac{1}{T}}\cdot e^{-j2\pi\epsilon}
\end{equation}

由于$|h(t)|^2$为实偶对称函数，因此其傅里叶变换$\mathfrak{F}[|h(t)|^2]$为实函数，且为正值。
于是我们得到了
\begin{equation}
  \mathbf{E}[\hat{\epsilon}]
  = -\frac{1}{2\pi}\angle \mathbf{E}[R(\frac{1}{T})]
  = -\frac{1}{2\pi}\angle (\frac{N}{T}\mathfrak{F}[|h(t)|^2]_{f=\frac{1}{T}}\cdot e^{-j2\pi\epsilon})
  = -\frac{1}{2\pi}\cdot (-j2 \pi \epsilon)
  = \epsilon
\end{equation}
说明了$\hat{\epsilon}$的确是定时偏差$\epsilon$的无偏估计。

\subsection{仿真结果与同步策略}

为了避免乘法、减小硬件资源成本，我们可以令$N=4$，即采样率是符号速率的4倍。
这样一来，公式简化为
\begin{equation}
  R(\frac{1}{T}) = r^2(0) - r^2(\frac{T}{2}) + j(r^2(\frac{3T}{4}) - r^2(\frac{T}{4}))
\end{equation}
相当于每四个采样点可以计算出一个定时偏差的估计，从而还原出符号点。

同时，由于估计过程中用到了数学期望$\mathbf{E}[a_n\cdot a_m^*] = \delta(m-n)$（实际还用到了噪声的期望为0），因此需要在时间上遍历更多的采样点，来等效得到取期望的效果。
因此，我们需要一段BPSK调制的信号来完成定时偏差的估计，而因为用到了$\mathbf{E}[a_n] = 0$的性质，因此这段BPSK符号序列应当尽可能地平均包含1，-1两种符号。

于是，我们使用1，-1交替的BPSK符号序列来完成定时偏差的估计。下面，我们通过仿真来观察这种估计算法的效果是否能满足同步要求，以及需要多少个符号可以收敛到同步要求内。

首先，我们取长度为100的1，-1交替的BPSK符号序列，并对每个符号点对应的4个采样点进行定时误差的计算，每个点处的最终定时误差估计是所有之前定时误差之和的平均值。
在观察定时误差随时间的曲线时，我们确认大约在10个符号后，定时误差就已经收敛到足够小的区间。
因此，我们对于具有不同初始定时误差的采样信号进行估计，并统计其在后90个符号点的定时偏差估计误差的均值和标准差，结果如下图所示。

注意：图中为了读图的方便，纵轴的单元为采样周期，并非符号周期，两者相差4倍。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/clock_offset_mean.png}
  \caption{符号同步中定时偏差的估计误差的均值}
  % %\label{fig:3-4}
\end{figure}

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/clock_offset_std.png}
  \caption{符号同步中定时偏差的估计误差的标准差}
  % %\label{fig:3-4}
\end{figure}

从估计误差的均值可以看出，上述前馈式的定时误差估计总体上是无偏的。
但随着采样点从左侧靠近符号点，均值有至多0.01个采样周期的负向锯齿形周期移动，目前暂不清楚其原因。
整体上看，就算信噪比低至15dB，估计的均值仍能控制在0.02个采样周期以内。

从估计误差的标准差可以看出，信噪比越低，估计误差的标准差越大，这是符合预期的。
在信噪比为15dB时，估计误差的标准差最高约为0.06个采样周期，而当信噪比变高时，标准差则下降明显。
同时观察到，在不同初始定时误差下，估计的标准差并不恒定，而是呈现出中间低两边高的趋势。
这是因为在初始定时误差靠近0或1时，首尾两端的采样点靠近信号的边缘，失去了前面理论分析中无穷求和的形式，为估计引入了额外误差。

我们可以以3倍标准差来计算，那么定时偏差的估计误差绝大部分在$3\times 0.06 + 0.02=0.2$个采样周期以内，即0.05个符号周期以内。
这是我们前面规定的同步要求的约1/3，说明这个估计算法可以很好地满足符号同步的需要。

接下来，我们同样通过仿真来决定符号同步需要多少个BPSK符号。
下图给出了不同信噪比条件下，随符号数增大，该符号点对应的定时偏差的估计误差最大值。
此处最大值使用了不同初始定时偏差的信号进行同步试验，可以完整地反映符号同步过程中的定时偏差估计的收敛状态。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/clock_offset_symbol_max.png}
  \caption{符号同步中定时偏差的估计误差的最大值}
  % %\label{fig:3-4}
\end{figure}

可以观察到，大约9个符号后，定时偏差的估计最大值不再显著减小，可以认为已经收敛。
在第9至12个符号点处，我们看到信噪比为15dB条件下估计误差的最大值为0.16个采样周期左右，这与我们之前利用标准差计算的误差范围相符，同样满足同步要求。
这说明符号同步使用9个BPSK符号已经足够。

此外，我们在待同步的信号上加上10kHz的频偏，发现并不影响上述仿真结果。
这与前面的理论分析也相吻合，因为我们使用的是采样点的幅度值，并未使用其相位信息。

回忆之前载波同步中的精同步阶段，它需要BPSK符号作为输入，同时不利用符号的幅度信息，只关心其相位信息。
符号同步中第一个符号点的最大0.5个采样周期（即0.125个符号周期）的估计误差对其相位信息影响不大。

这样一来，我们发现符号同步和载波同步中的精同步阶段可以同时进行，符号同步中利用定时频偏恢复出的BPSK符号恰可以作为载波同步的输入。
为了留出一定裕量，我们决定在使用10个1，-1交替的BPSK符号，称为S1序列。

\subsection{符号还原}

光有定时偏差的估计是不够的。为了给载波同步、以及后续消息正文的解码提供符号点，我们还需要根据定时误差进行符号还原。
以下是几种常用的符号还原策略：

\begin{itemize}
  \item 最邻近采样：即取距离定时偏差最近的采样点作为符号点
  \item 相邻采样的线性插值：即取距离定时偏差相近的两个采样点，根据它们与定时偏差的距离，进行线性插值（加权平均）
  \item 拉格朗日插值：即取距离定时偏差相近的左右各两个采样点，利用三阶多项式形式的拉格朗日插值公式进行插值。公式如下
  \begin{equation}
    x(\mu) = \sum_{k=-1}^{2} [x(kT/N) \cdot \prod_{i=-1, i\ne k}^{2} \frac{i+\mu}{i-k}]
  \end{equation}
  其中$\mu=\lfloor4(1-\epsilon)\rfloor$为符号点相对于其最近的左侧采样点的距离（单位为采样周期）
\end{itemize}

显然，这三种符号还原策略从上至下还原准确度越来越高，但计算成本也越来越高。

下面，我们来评估这三种符号还原策略的性能是否满足我们的需要。
首先是最邻近采样，在采样速率为符号速率4倍的前提下，其可造成的最大符号时钟偏差为0.5个采样周期，即0.125个符号周期。
如果加上前面定时偏差估计的最大误差0.05个符号周期，则总的最大符号时钟偏差为0.175个符号周期，已经超过了我们的同步要求。

其次是相邻采样的线性插值。
经过枚举可能出现的符号序列，我们发现在定时偏差估计发生最大误差的前提下，线性插值的最大符号时钟偏差为0.5个采样周期，即0.125个符号周期，满足同步要求。
（对应于定时偏差为0.125、0.375、0.625、0.875时，采样点均匀位于符号点两侧，进行线性插值后幅度值不变，相当于偏差为0.5个采样周期）

由于拉格朗日插值较线性插值更接近符号点，同步要求必然满足。

在实机验证中，线性插值和拉格朗日插值均能支持后续的解码，性能差异并不明显。
由于论文工作重点在协议的设计与实现，因此这里不对各种符号还原算法的性能进行深入的分析与比较，仅做原理性的介绍。
在实际应用中，使用者可以自主选择符号还原策略，协议对此不作限制。

\section{自动增益控制}

自动增益控制（Automatic Gain Control, AGC）是解码QAM调制符号的一个重要前提，它将符号的能量归一化，从而使得符号与编码时的星座图在幅度上对应，便于实现基于星座图的判决。

在本工作中，自动增益控制的实现方式相当简单直接，即在S1序列进行载波精同步和符号同步的同时，将符号同步得到的共10个符号的幅度值（均应为1）取平均，作为后续符号的归一化因子。

这种实现方式有两个前提，一是信号强度在整个消息长度内基本不变，二是符号同步得到的符号幅度相对稳定、可以代表“幅度1”。

对于第一个前提，由于单个消息的时间尺度为100us，而低空飞行器的最大速度为100m/s，因此在发射或接收单个消息期间，飞行器运行的距离最大为
\begin{equation}
    d = v \times t = 100\text{m/s} \times 100\text{us} = 1\text{cm}
\end{equation}
而在1厘米的距离范围内，无线信道的特性变化可以忽略不计，因此信号强度可以认为基本不变。

对于第二个前提，我们通过实机验证了这一点。
在自动增益控制单元获得的10个符号中，前两个符号由于受S0序列和符号同步误差的影响与其他符号的幅度值偏差较大。
在排除这两个符号后，其余8个符号的幅度值非常稳定，在各个信噪比条件下均可以成功归一化符号并生成幅度正确的星座图。

\section{消息头检测}

由于在信号检测或符号同步时，接收机有可能提前或延后计数了1个符号。
因此为了明确消息正文的起始位置，同时也避免信号检测器的误判，我们在前导码的最后加入了7位BPSK调制的Barker码进行消息头检测，或者称为帧同步。

Barker码具有良好的自相关特性，只在码型完全同步时自相关函数才具有幅度与长度相同的主瓣，而在其余偏移位置幅度则为0或-1（在已完成自动增益控制的前提下）
同时，其码型不易从噪声中生成，且与前导码的其他部分码型、消息正文的16QAM调制符号均不同，因此可以有效地区分出这段码字。

下图给出了7位Barker码的码型和自相关特性。
我们可以将阈值设为5，在实机验证中可以成功检测出消息头，同时也有效地停止了多起信号检测器的误检事件。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/barker.png}
  \caption{7位Barker码的码型和自相关特性}
  % %\label{fig:3-6}
\end{figure}
