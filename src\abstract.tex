% !TeX root = ../main.tex

% 中英文摘要和关键字

\begin{abstract}
  随着低空经济的快速发展，低空飞行器数量急剧增长，空域管理面临前所未有的挑战。为确保低空飞行器的安全运行，需要建立有效的监视和通信系统。本文针对低空飞行器空域管理需求，设计并实现了一种基于L波段的广播式通信协议。

  本文首先分析了低空经济发展背景下的空域管理需求，参考民用航空ADS-B系统的设计理念，确定了通信协议的总体技术方案。选择1443MHz作为中心频率，带宽2MHz，采用16QAM调制方式，符号速率1.5Msps。协议设计涵盖物理层、编码层和网络层三个层面。

  在物理层设计中，提出了基于双滑动窗法的信号检测算法，设计了载波同步和符号同步方案，采用7位Barker码实现消息头检测。在编码层设计中，定义了报文内容和结构，采用LDPC码进行信道编码以提高传输可靠性。在网络层设计中，利用GPS授时实现时钟同步，设计了时隙划分和碰撞检测机制。

  通过仿真验证了协议各层的性能，并与ADS-B系统进行了对比分析。仿真结果表明，所设计的协议在信号检测、同步性能和抗干扰能力方面均达到预期目标。最后，使用PlutoSDR软件无线电平台对协议进行了实机验证，证明了协议设计的可行性和有效性。

  本研究为低空飞行器通信系统提供了一种实用的技术方案，对推动低空经济发展具有重要意义。

  % 关键词用"英文逗号"分隔，输出时会自动处理为正确的分隔符
  \thusetup{
    keywords = {低空飞行器, L波段通信, 广播式协议, ADS-B, 软件无线电},
  }
\end{abstract}

\begin{abstract*}
  With the rapid development of the low-altitude economy, the number of low-altitude aircraft has increased dramatically, posing unprecedented challenges to airspace management. To ensure the safe operation of low-altitude aircraft, effective surveillance and communication systems need to be established. This paper addresses the airspace management requirements for low-altitude aircraft and designs and implements an L-band broadcast communication protocol.

  This paper first analyzes the airspace management requirements under the background of low-altitude economic development, and determines the overall technical scheme of the communication protocol by referring to the design concept of the civil aviation ADS-B system. The center frequency of 1443MHz, bandwidth of 2MHz, 16QAM modulation, and symbol rate of 1.5Msps are selected. The protocol design covers three levels: physical layer, coding layer, and network layer.

  In the physical layer design, a signal detection algorithm based on the dual sliding window method is proposed, carrier synchronization and symbol synchronization schemes are designed, and 7-bit Barker codes are used to implement message header detection. In the coding layer design, the message content and structure are defined, and LDPC codes are used for channel coding to improve transmission reliability. In the network layer design, GPS timing is used to achieve clock synchronization, and time slot division and collision detection mechanisms are designed.

  The performance of each layer of the protocol is verified through simulation, and a comparative analysis with the ADS-B system is conducted. The simulation results show that the designed protocol achieves the expected goals in signal detection, synchronization performance, and anti-interference capability. Finally, the PlutoSDR software-defined radio platform is used to verify the protocol in real machines, proving the feasibility and effectiveness of the protocol design.

  This research provides a practical technical solution for low-altitude aircraft communication systems and is of great significance for promoting the development of the low-altitude economy.

  % Use comma as separator when inputting
  \thusetup{
    keywords* = {Low-altitude aircraft, L-band communication, Broadcast protocol, ADS-B, Software-defined radio},
  }
\end{abstract*}
