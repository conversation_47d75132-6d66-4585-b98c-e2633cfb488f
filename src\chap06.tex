% !TeX root = ../main.tex

\chapter{实机验证}

至此，协议的设计内容基本完成。
下面，我们选择使用软件无线电平台PlutoSDR在真实物理环境中验证协议的可行性。

软件无线电平台指的是通过软件控制工作频率、系统带宽、调制方式等的无线电设备，其对发射和接收信号的类型具有相当大的灵活性，因此可以作为一个通用的射频收发平台来支持新协议的开发与测试。

PlutoSDR是一款由ADI公司开发的低成本、高性能的软件无线电平台，广泛应用于科研、教学以及业余无线电等领域。
以下是它的部分硬件参数：

\begin{table}[htbp]
  \centering
  \caption{PlutoSDR硬件参数}
  \label{tab:6-1}
  \begin{tabular}{|c|c|}
    \hline
    参数 & 值 \\
    \hline
    架构 & 零中频 \\
    \hline
    射频芯片 & AD9363 \\
    \hline
    数字信号处理器 & Zynq-7010 \\
    \hline
    收发通道数 & $2\times2$ \\
    \hline
    中心频率范围 & 321MHz~3.8GHz \\
    \hline
    最大射频带宽 & 20MHz \\
    \hline
    最高采样率 & 61.44Msps \\
    \hline
    采样深度 & 12bit \\
    \hline
    增益范围 & 0dB~74.5dB \\
    \hline
  \end{tabular}
\end{table}

在PlutoSDR上进行验证时，需注意将其接收机增益设置为人工输入（"manual"），并将其设置为30dB，以避免PlutoSDR内部的自动增益控制单元调整信号导致的信号失真。

以下是验证得到的星座图，观察到同步效果正常，接收消息无误码，说明协议设计在实际物理环境中是可行的。

\begin{figure}[htbp]
  \centering
  \includegraphics[width=0.8\textwidth]{figures/Figure_6.png}
  \caption{实机验证的星座图}
  % %\label{fig:3-5}
\end{figure}
